#!/usr/bin/env python3
"""
代理功能测试脚本
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from collector import RuleCollector

def test_proxy_configuration():
    """测试代理配置功能"""
    print("=== 代理配置测试 ===\n")
    
    # 测试配置
    config_with_proxy = {
        'proxy': {
            'http_proxy': 'http://127.0.0.1:6152',
            'https_proxy': 'http://127.0.0.1:6152',
            'proxy_domains': [
                'github.com',
                'githubusercontent.com',
                'cdn.jsdelivr.net'
            ],
            'no_proxy_domains': [
                'localhost',
                '127.0.0.1'
            ]
        },
        'settings': {
            'user_agent': 'TestAgent/1.0',
            'timeout': 30,
            'retry_count': 2,
            'concurrent_downloads': 3
        },
        'deduplication': {
            'enable_collection_dedup': True
        }
    }
    
    collector = RuleCollector(config_with_proxy)
    
    # 测试URL代理判断
    test_urls = [
        'https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Surge/Telegram/Telegram.list',
        'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/OpenAI/OpenAI.list',
        'https://github.com/blackmatrix7/ios_rule_script/raw/master/rule/Surge/Claude/Claude.list',
        'http://localhost:8080/test.list',
        'https://example.com/rules.list'
    ]
    
    print("URL代理判断测试:")
    for url in test_urls:
        should_proxy = collector.should_use_proxy(url)
        proxies = collector.get_proxies_for_url(url)
        status = "使用代理" if should_proxy else "直连"
        proxy_info = f" ({proxies})" if proxies else ""
        print(f"  {url}")
        print(f"    -> {status}{proxy_info}")
    
    return collector

def test_proxy_access():
    """测试代理访问功能"""
    print("\n=== 代理访问测试 ===\n")
    
    collector = test_proxy_configuration()
    
    # 测试实际访问（需要代理服务器运行在127.0.0.1:6152）
    test_url = 'https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Surge/Telegram/Telegram.list'
    
    print(f"尝试通过代理访问: {test_url}")
    
    try:
        content = collector.fetch_rule_content(test_url)
        if content:
            lines = content.split('\n')
            print(f"✅ 成功获取内容 ({len(lines)} 行)")
            print("前10行内容:")
            for i, line in enumerate(lines[:10], 1):
                print(f"  {i:2d}: {line}")
            if len(lines) > 10:
                print(f"  ... 还有 {len(lines) - 10} 行")
        else:
            print("❌ 获取内容为空")
    except Exception as e:
        print(f"❌ 访问失败: {e}")
        print("请确保代理服务器运行在 http://127.0.0.1:6152")

def test_multi_source_with_proxy():
    """测试多源收集与代理结合"""
    print("\n=== 多源收集+代理测试 ===\n")
    
    config = {
        'categories': {
            'telegram': {
                'name': 'Telegram',
                'description': 'Telegram相关域名和IP',
                'output_file': 'telegram.list',
                'sources': [
                    {
                        'name': 'blackmatrix7-telegram',
                        'url': 'https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Surge/Telegram/Telegram.list'
                    }
                ]
            }
        },
        'proxy': {
            'http_proxy': 'http://127.0.0.1:6152',
            'https_proxy': 'http://127.0.0.1:6152',
            'proxy_domains': [
                'cdn.jsdelivr.net',
                'github.com',
                'githubusercontent.com'
            ]
        },
        'settings': {
            'user_agent': 'TestAgent/1.0',
            'timeout': 30,
            'retry_count': 2,
            'concurrent_downloads': 2
        },
        'deduplication': {
            'enable_collection_dedup': True
        }
    }
    
    collector = RuleCollector(config)
    
    try:
        print("开始多源收集测试...")
        rules_data = collector.collect_all_rules()
        
        print("收集结果:")
        for category_name, (stats, rules) in rules_data.items():
            print(f"  {category_name}: {len(rules)} 条规则")
            
            # 显示统计信息
            for rule_type, count in sorted(stats.items()):
                if rule_type != 'TOTAL':
                    print(f"    - {rule_type}: {count}")
            
            # 显示前几条规则
            if rules:
                print("    示例规则:")
                for rule in rules[:5]:
                    print(f"      - {rule}")
                if len(rules) > 5:
                    print(f"      ... 还有 {len(rules) - 5} 条规则")
        
        print("✅ 多源收集+代理测试成功")
        
    except Exception as e:
        print(f"❌ 多源收集测试失败: {e}")

def demo_proxy_config():
    """演示代理配置"""
    print("=== 代理配置说明 ===\n")
    
    print("在 config.yaml 中添加代理配置:")
    print("""
proxy:
  # 全局代理设置
  http_proxy: "http://127.0.0.1:6152"
  https_proxy: "http://127.0.0.1:6152"
  
  # 需要使用代理的域名列表
  proxy_domains:
    - "github.com"
    - "githubusercontent.com"
    - "cdn.jsdelivr.net"
  
  # 不使用代理的域名列表（优先级高于proxy_domains）
  no_proxy_domains:
    - "localhost"
    - "127.0.0.1"
""")
    
    print("配置说明:")
    print("1. http_proxy/https_proxy: 代理服务器地址")
    print("2. proxy_domains: 需要通过代理访问的域名列表")
    print("3. no_proxy_domains: 不使用代理的域名列表（优先级更高）")
    print("4. 如果URL的域名在proxy_domains中，将使用代理")
    print("5. 如果URL的域名在no_proxy_domains中，将直连（不使用代理）")

if __name__ == '__main__':
    demo_proxy_config()
    
    print("\n" + "="*60)
    
    test_proxy_configuration()
    
    print("\n" + "="*60)
    
    # 注意：以下测试需要代理服务器运行在 127.0.0.1:6152
    print("注意: 以下测试需要代理服务器运行在 http://127.0.0.1:6152")
    
    try:
        test_proxy_access()
        print("\n" + "="*60)
        test_multi_source_with_proxy()
        
        print("\n🎉 代理功能测试完成！")
        
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        print("请检查代理服务器是否正常运行")
