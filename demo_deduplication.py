#!/usr/bin/env python3
"""
去重功能演示脚本
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from deduplicator import RuleDeduplicator

def demo_deduplication():
    """演示去重功能"""
    
    print("=== Surge规则去重功能演示 ===\n")
    
    # 创建去重器
    deduplicator = RuleDeduplicator()
    
    # 测试数据：包含重复和冗余规则
    test_rules = [
        # 完全重复的规则
        'DOMAIN,example.com',
        'DOMAIN,example.com',  # 重复
        
        # 格式不同但内容相同的规则
        'DOMAIN-SUFFIX,test.com',
        'DOMAIN-SUFFIX,  test.com  ',  # 空格不同
        
        # IP规则重复（no-resolve格式不同）
        'IP-CIDR,*******/32,no-resolve',
        'IP-CIDR,*******/32, no-resolve',  # 空格不同
        
        # 冗余规则：DOMAIN被DOMAIN-SUFFIX覆盖
        'DOMAIN,api.google.com',
        'DOMAIN,mail.google.com',
        'DOMAIN-SUFFIX,google.com',  # 这个会覆盖上面两个
        
        # 冗余规则：更具体的DOMAIN-SUFFIX被通用的覆盖
        'DOMAIN-SUFFIX,accounts.google.com',
        'DOMAIN-SUFFIX,apis.google.com',
        # google.com已经存在，会覆盖这些
        
        # 不冗余的规则
        'DOMAIN,facebook.com',
        'DOMAIN-SUFFIX,twitter.com',
        'IP-ASN,13335,no-resolve',
        'PROCESS-NAME,telegram',
        
        # Telegram相关规则
        'DOMAIN,t.me',
        'DOMAIN-SUFFIX,telegram.org',
        'IP-CIDR,*************/20,no-resolve',
    ]
    
    print(f"原始规则数量: {len(test_rules)}")
    print("原始规则列表:")
    for i, rule in enumerate(test_rules, 1):
        print(f"  {i:2d}. {rule}")
    
    print("\n" + "="*50)
    
    # 执行去重
    deduplicated_rules, stats = deduplicator.deduplicate_rules(test_rules, enable_advanced=True)
    
    print(f"\n去重后规则数量: {len(deduplicated_rules)}")
    print("去重后规则列表:")
    for i, rule in enumerate(deduplicated_rules, 1):
        print(f"  {i:2d}. {rule}")
    
    print(f"\n去重统计:")
    print(f"  - 原始规则数: {stats['original']}")
    print(f"  - 最终规则数: {stats['deduplicated']}")
    print(f"  - 移除规则数: {stats['removed']}")
    print(f"  - 基本去重移除: {stats['basic_removed']} (完全重复)")
    print(f"  - 高级去重移除: {stats['advanced_removed']} (冗余规则)")
    
    print("\n" + "="*50)
    
    # 演示分类去重
    print("\n=== 分类去重演示 ===")
    
    classified_rules = {
        'telegram': [
            'DOMAIN,t.me',
            'DOMAIN,t.me',  # 重复
            'DOMAIN-SUFFIX,telegram.org',
            'DOMAIN,api.telegram.org',  # 被DOMAIN-SUFFIX覆盖
            'IP-CIDR,*************/20,no-resolve',
        ],
        'foreign_domains': [
            'DOMAIN,google.com',
            'DOMAIN,google.com',  # 重复
            'DOMAIN-SUFFIX,google.com',  # 覆盖上面的DOMAIN
            'DOMAIN,facebook.com',
            'DOMAIN-SUFFIX,twitter.com',
        ]
    }
    
    print("分类前:")
    for category, rules in classified_rules.items():
        print(f"  {category}: {len(rules)} 条规则")
        for rule in rules:
            print(f"    - {rule}")
    
    # 执行分类去重
    deduplicated_classified, all_stats = deduplicator.deduplicate_by_category(classified_rules, enable_advanced=True)
    
    print("\n分类后:")
    for category, rules in deduplicated_classified.items():
        stats = all_stats[category]
        print(f"  {category}: {len(rules)} 条规则 (原始: {stats['original']}, 移除: {stats['removed']})")
        for rule in rules:
            print(f"    - {rule}")
    
    print("\n✅ 去重功能演示完成!")

if __name__ == '__main__':
    demo_deduplication()
