#!/usr/bin/env python3
"""
测试注释去重功能
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from deduplicator import RuleDeduplicator

def test_comment_deduplication():
    """测试注释去重功能"""
    print("=== 注释去重功能测试 ===\n")
    
    deduplicator = RuleDeduplicator()
    
    # 测试数据：包含相同IP-ASN但注释不同的规则
    test_rules = [
        # 相同ASN，不同注释
        'IP-ASN,63561 // China Electronics Technology Cyber Security Co.,LTD',
        'IP-ASN,63561 // 中国电子科技网络安全有限公司',
        
        # 相同ASN，一个有注释一个没有
        'IP-ASN,4134,no-resolve',
        'IP-ASN,4134 // 中国电信',
        
        # 相同ASN，格式不同
        'IP-ASN,9808,no-resolve // China Mobile',
        'IP-ASN,9808 // 中国移动通信集团公司',
        
        # 不同ASN，应该保留
        'IP-ASN,4837 // 中国联通',
        'IP-ASN,17623 // 中国联通',
        
        # 其他类型规则测试
        'DOMAIN,example.com // 示例域名',
        'DOMAIN,example.com // Example domain',
        
        # IP-CIDR规则测试
        'IP-CIDR,*******/32,no-resolve // Cloudflare DNS',
        'IP-CIDR,*******/32,no-resolve // Cloudflare公共DNS',
    ]
    
    print(f"原始规则数量: {len(test_rules)}")
    print("原始规则列表:")
    for i, rule in enumerate(test_rules, 1):
        print(f"  {i:2d}. {rule}")
    
    print("\n" + "="*60)
    
    # 测试规则键提取
    print("\n规则键提取测试:")
    for rule in test_rules:
        key = deduplicator.extract_rule_key(rule)
        print(f"  规则: {rule}")
        print(f"  键值: {key}")
        print()
    
    print("="*60)
    
    # 执行去重
    deduplicated_rules, stats = deduplicator.deduplicate_rules(test_rules, enable_advanced=False)
    
    print(f"\n去重后规则数量: {len(deduplicated_rules)}")
    print("去重后规则列表:")
    for i, rule in enumerate(deduplicated_rules, 1):
        print(f"  {i:2d}. {rule}")
    
    print(f"\n去重统计:")
    print(f"  - 原始规则数: {stats['original']}")
    print(f"  - 最终规则数: {stats['deduplicated']}")
    print(f"  - 移除规则数: {stats['removed']}")
    
    # 验证去重结果
    print("\n" + "="*60)
    print("\n验证去重结果:")
    
    # 检查IP-ASN,63561是否只保留一条
    asn_63561_rules = [rule for rule in deduplicated_rules if 'IP-ASN,63561' in rule]
    print(f"IP-ASN,63561 规则数量: {len(asn_63561_rules)}")
    if asn_63561_rules:
        print(f"保留的规则: {asn_63561_rules[0]}")
        # 检查是否保留了中文注释
        if '中国电子科技' in asn_63561_rules[0]:
            print("✅ 正确保留了中文注释版本")
        else:
            print("⚠️  未保留中文注释版本")
    
    # 检查IP-ASN,4134是否只保留一条
    asn_4134_rules = [rule for rule in deduplicated_rules if 'IP-ASN,4134' in rule]
    print(f"\nIP-ASN,4134 规则数量: {len(asn_4134_rules)}")
    if asn_4134_rules:
        print(f"保留的规则: {asn_4134_rules[0]}")
        if '中国电信' in asn_4134_rules[0]:
            print("✅ 正确保留了有注释的版本")
        else:
            print("⚠️  未保留有注释的版本")
    
    # 检查不同ASN是否都保留
    asn_4837_rules = [rule for rule in deduplicated_rules if 'IP-ASN,4837' in rule]
    asn_17623_rules = [rule for rule in deduplicated_rules if 'IP-ASN,17623' in rule]
    print(f"\nIP-ASN,4837 规则数量: {len(asn_4837_rules)} (应该为1)")
    print(f"IP-ASN,17623 规则数量: {len(asn_17623_rules)} (应该为1)")
    
    return len(test_rules) - len(deduplicated_rules)

def test_rule_selection():
    """测试规则选择逻辑"""
    print("\n=== 规则选择逻辑测试 ===\n")
    
    deduplicator = RuleDeduplicator()
    
    test_cases = [
        # 中文 vs 英文注释
        ('IP-ASN,63561 // China Electronics Technology', 'IP-ASN,63561 // 中国电子科技网络安全有限公司'),
        
        # 有注释 vs 无注释
        ('IP-ASN,4134,no-resolve', 'IP-ASN,4134 // 中国电信'),
        
        # 长注释 vs 短注释
        ('IP-ASN,9808 // CM', 'IP-ASN,9808 // China Mobile Communications Group'),
        
        # 相同长度
        ('IP-ASN,4837 // CU', 'IP-ASN,4837 // CN'),
    ]
    
    for i, (rule1, rule2) in enumerate(test_cases, 1):
        better_rule = deduplicator.choose_better_rule(rule1, rule2)
        print(f"测试 {i}:")
        print(f"  规则1: {rule1}")
        print(f"  规则2: {rule2}")
        print(f"  选择: {better_rule}")
        
        # 分析选择原因
        has_chinese1 = bool(__import__('re').search(r'[\u4e00-\u9fff]', rule1))
        has_chinese2 = bool(__import__('re').search(r'[\u4e00-\u9fff]', rule2))
        
        if has_chinese1 and not has_chinese2:
            reason = "选择中文注释版本"
        elif has_chinese2 and not has_chinese1:
            reason = "选择中文注释版本"
        elif len(rule1) > len(rule2):
            reason = "选择更长的版本"
        elif len(rule2) > len(rule1):
            reason = "选择更长的版本"
        else:
            reason = "选择第一个版本"
        
        print(f"  原因: {reason}")
        print()

def main():
    """主函数"""
    print("🔧 IP-ASN注释去重功能测试")
    print("="*60)
    
    removed_count = test_comment_deduplication()
    
    print("\n" + "="*60)
    test_rule_selection()
    
    print("="*60)
    print(f"\n✅ 测试完成！成功去重 {removed_count} 条重复规则")
    
    print("\n💡 功能说明:")
    print("1. 🔍 智能识别IP-ASN规则的ASN号码")
    print("2. 🔄 相同ASN号码的规则会被去重")
    print("3. 🇨🇳 优先保留中文注释版本")
    print("4. 📝 优先保留有注释的版本")
    print("5. 📏 优先保留信息更丰富的版本")
    
    print("\n🎯 实际应用:")
    print("• IP-ASN,63561 // China Electronics Technology")
    print("• IP-ASN,63561 // 中国电子科技网络安全有限公司")
    print("→ 保留: IP-ASN,63561 // 中国电子科技网络安全有限公司")

if __name__ == '__main__':
    main()
