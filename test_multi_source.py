#!/usr/bin/env python3
"""
多源收集功能测试脚本
"""

import sys
import os
import yaml
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from collector import RuleCollector
from classifier import RuleClassifier
from generator import RuleFileGenerator

def create_test_config():
    """创建测试配置"""
    return {
        'categories': {
            'telegram': {
                'name': 'Telegram',
                'description': 'Telegram相关域名和IP',
                'output_file': 'telegram.list',
                'sources': [
                    {
                        'name': 'test-source-1',
                        'url': 'https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Surge/Telegram/Telegram.list'
                    }
                ]
            },
            'ai_services': {
                'name': 'AI Services',
                'description': 'AI服务相关域名和IP',
                'output_file': 'ai_services.list',
                'sources': [
                    {
                        'name': 'openai-source',
                        'url': 'https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Surge/OpenAI/OpenAI.list'
                    },
                    {
                        'name': 'claude-source',
                        'url': 'https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Surge/Claude/Claude.list'
                    }
                ]
            }
        },
        'output': {
            'directory': 'test_rules',
            'format': 'surge',
            'include_stats': True,
            'include_header': True
        },
        'deduplication': {
            'enable_basic': True,
            'enable_advanced': True,
            'enable_collection_dedup': True,
            'enable_classification_dedup': True,
            'enable_generation_dedup': True
        },
        'settings': {
            'user_agent': 'TestAgent/1.0',
            'timeout': 30,
            'retry_count': 2,
            'concurrent_downloads': 3
        }
    }

def test_multi_source_collection():
    """测试多源收集功能"""
    print("=== 多源收集功能测试 ===\n")
    
    # 创建测试配置
    config = create_test_config()
    
    print("测试配置:")
    for category_name, category_config in config['categories'].items():
        sources = category_config.get('sources', [])
        print(f"  {category_name}: {len(sources)} 个数据源")
        for source in sources:
            print(f"    - {source['name']}: {source['url']}")
    
    print("\n" + "="*60)
    
    # 测试收集器
    print("\n1. 测试收集器...")
    collector = RuleCollector(config)
    
    try:
        rules_data = collector.collect_all_rules()
        
        print(f"收集结果:")
        total_rules = 0
        for category_name, (stats, rules) in rules_data.items():
            print(f"  {category_name}: {len(rules)} 条规则")
            total_rules += len(rules)
            
            # 显示统计信息
            for rule_type, count in sorted(stats.items()):
                if rule_type != 'TOTAL':
                    print(f"    - {rule_type}: {count}")
        
        print(f"总规则数: {total_rules}")
        
    except Exception as e:
        print(f"收集器测试失败: {e}")
        return False
    
    print("\n" + "="*60)
    
    # 测试分类器
    print("\n2. 测试分类器...")
    classifier = RuleClassifier(config)
    
    try:
        classified_rules = classifier.classify_rules(rules_data)
        
        print(f"分类结果:")
        for category, rules in classified_rules.items():
            print(f"  {category}: {len(rules)} 条规则")
            
            # 显示前几条规则作为示例
            if rules:
                print("    示例规则:")
                for rule in rules[:3]:
                    print(f"      - {rule}")
                if len(rules) > 3:
                    print(f"      ... 还有 {len(rules) - 3} 条规则")
        
    except Exception as e:
        print(f"分类器测试失败: {e}")
        return False
    
    print("\n" + "="*60)
    
    # 测试生成器
    print("\n3. 测试生成器...")
    generator = RuleFileGenerator(config)
    
    try:
        # 确保输出目录存在
        os.makedirs('test_rules', exist_ok=True)
        
        generated_files = generator.generate_all_files(classified_rules)
        
        print(f"生成文件:")
        for category, filepath in generated_files.items():
            filename = os.path.basename(filepath)
            rule_count = len(classified_rules.get(category, []))
            print(f"  {category}: {filename} ({rule_count} 条规则)")
            
            # 显示文件前几行
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    lines = f.readlines()[:10]
                    print("    文件内容预览:")
                    for line in lines:
                        print(f"      {line.rstrip()}")
                    if len(lines) >= 10:
                        print("      ...")
        
        # 生成汇总文件
        generator.generate_summary_file(classified_rules, generated_files)
        print(f"  汇总文件: test_rules/README.md")
        
    except Exception as e:
        print(f"生成器测试失败: {e}")
        return False
    
    print("\n✅ 多源收集功能测试完成!")
    return True

def demo_config_structure():
    """演示新的配置结构"""
    print("\n=== 新配置结构演示 ===")
    
    config = create_test_config()
    
    print("新的配置结构特点:")
    print("1. 按分类组织数据源，而不是按数据源组织规则")
    print("2. 每个分类可以配置多个数据源URL")
    print("3. 自动汇总和去重同一分类的多个数据源")
    
    print("\n配置示例:")
    print(yaml.dump(config['categories'], default_flow_style=False, allow_unicode=True))

if __name__ == '__main__':
    demo_config_structure()
    
    print("\n" + "="*80)
    
    success = test_multi_source_collection()
    
    if success:
        print("\n🎉 所有测试通过！多源收集功能正常工作。")
    else:
        print("\n❌ 测试失败，请检查网络连接或配置。")
        sys.exit(1)
