# Surge规则收集器配置文件

# 数据源配置
sources:
  # blackmatrix7的规则库
  blackmatrix7:
    base_url: "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Surge"
    rules:
      - name: "Telegram"
        path: "Telegram/Telegram.list"
        category: "telegram"
      - name: "China"
        path: "China/China.list"
        category: "china_domains"
      - name: "ChinaASN"
        path: "ChinaASN/ChinaASN.list"
        category: "china_asn"
      - name: "Global"
        path: "Global/Global.list"
        category: "foreign_domains"
      - name: "Apple"
        path: "Apple/Apple.list"
        category: "foreign_domains"
      - name: "Google"
        path: "Google/Google.list"
        category: "foreign_domains"
      - name: "Microsoft"
        path: "Microsoft/Microsoft.list"
        category: "foreign_domains"
      - name: "OpenAI"
        path: "OpenAI/OpenAI.list"
        category: "ai_services"
      - name: "<PERSON>"
        path: "Claude/Claude.list"
        category: "ai_services"

# 分类配置
categories:
  china_asn:
    name: "China IP-ASN"
    description: "中国大陆IP地址段和ASN"
    output_file: "china_asn.list"
  
  china_domains:
    name: "China Domains"
    description: "中国大陆常见域名"
    output_file: "china_domains.list"
  
  foreign_domains:
    name: "Foreign Domains"
    description: "国外常见域名"
    output_file: "foreign_domains.list"
  
  telegram:
    name: "Telegram"
    description: "Telegram相关域名和IP"
    output_file: "telegram.list"
  
  ai_services:
    name: "AI Services"
    description: "AI服务相关域名和IP"
    output_file: "ai_services.list"

# 输出配置
output:
  directory: "rules"
  format: "surge"
  include_stats: true
  include_header: true

# 其他配置
settings:
  user_agent: "SurgeRuleCollector/1.0"
  timeout: 30
  retry_count: 3
  concurrent_downloads: 5
