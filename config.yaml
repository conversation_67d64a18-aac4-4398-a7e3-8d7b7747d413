# Surge规则收集器配置文件

# 数据源配置 - 按分类组织多个数据源
sources:

# 分类配置 - 每个分类包含多个数据源
categories:
  china_asn:
    name: "China IP-ASN"
    description: "中国大陆IP地址段和ASN"
    output_file: "china_asn.list"
    sources:
      - name: "blackmatrix7-ChinaASN"
        url: "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Surge/ChinaASN/ChinaASN.list"
      - name: "Loyalsoldier-CN-cidr"
        url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/surge-rules@release/cncidr.txt"

  china_domains:
    name: "China Domains"
    description: "中国大陆常见域名"
    output_file: "china_domains.list"
    sources:
      - name: "blackmatrix7-China"
        url: "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Surge/China/China.list"
      - name: "Loyalsoldier-Direct"
        url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/surge-rules@release/direct.txt"

  foreign_domains:
    name: "Foreign Domains"
    description: "国外常见域名"
    output_file: "foreign_domains.list"
    sources:
      - name: "blackmatrix7-Global"
        url: "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Surge/Global/Global.list"
      - name: "blackmatrix7-Apple"
        url: "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Surge/Apple/Apple.list"
      - name: "blackmatrix7-Google"
        url: "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Surge/Google/Google.list"
      - name: "blackmatrix7-Microsoft"
        url: "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Surge/Microsoft/Microsoft.list"
      - name: "Loyalsoldier-Proxy"
        url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/surge-rules@release/proxy.txt"

  telegram:
    name: "Telegram"
    description: "Telegram相关域名和IP"
    output_file: "telegram.list"
    sources:
      - name: "blackmatrix7-Telegram"
        url: "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Surge/Telegram/Telegram.list"
      - name: "v2fly-Telegram"
        url: "https://cdn.jsdelivr.net/gh/v2fly/domain-list-community@release/telegram.txt"
      - name: "ACL4SSR-Telegram"
        url: "https://cdn.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Telegram.list"

  ai_services:
    name: "AI Services"
    description: "AI服务相关域名和IP"
    output_file: "ai_services.list"
    sources:
      - name: "blackmatrix7-OpenAI"
        url: "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Surge/OpenAI/OpenAI.list"
      - name: "blackmatrix7-Claude"
        url: "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Surge/Claude/Claude.list"
      - name: "blackmatrix7-Gemini"
        url: "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Surge/Gemini/Gemini.list"

# 输出配置
output:
  directory: "rules"
  format: "surge"
  include_stats: true
  include_header: true

# 去重配置
deduplication:
  enable_basic: true # 启用基本去重（移除完全相同的规则）
  enable_advanced: true # 启用高级去重（移除冗余规则）
  enable_collection_dedup: true # 在收集阶段进行去重
  enable_classification_dedup: true # 在分类阶段进行去重
  enable_generation_dedup: true # 在生成阶段进行最终去重

# 其他配置
settings:
  user_agent: "SurgeRuleCollector/1.0"
  timeout: 30
  retry_count: 3
  concurrent_downloads: 5
