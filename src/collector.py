"""
Surge规则收集器模块
负责从各种数据源收集Surge规则
"""

import requests
import time
import logging
from typing import List, Dict, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib.parse import urljoin
import re


class RuleCollector:
    """规则收集器类"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': config.get('settings', {}).get('user_agent', 'SurgeRuleCollector/1.0')
        })
        self.timeout = config.get('settings', {}).get('timeout', 30)
        self.retry_count = config.get('settings', {}).get('retry_count', 3)
        self.concurrent_downloads = config.get('settings', {}).get('concurrent_downloads', 5)
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def fetch_rule_content(self, url: str) -> Optional[str]:
        """
        获取规则文件内容
        
        Args:
            url: 规则文件URL
            
        Returns:
            规则文件内容，失败返回None
        """
        for attempt in range(self.retry_count):
            try:
                self.logger.info(f"正在获取规则: {url} (尝试 {attempt + 1}/{self.retry_count})")
                response = self.session.get(url, timeout=self.timeout)
                response.raise_for_status()
                
                content = response.text.strip()
                if content:
                    self.logger.info(f"成功获取规则: {url}")
                    return content
                else:
                    self.logger.warning(f"规则文件为空: {url}")
                    
            except requests.exceptions.RequestException as e:
                self.logger.warning(f"获取规则失败 (尝试 {attempt + 1}/{self.retry_count}): {url} - {e}")
                if attempt < self.retry_count - 1:
                    time.sleep(2 ** attempt)  # 指数退避
        
        self.logger.error(f"无法获取规则: {url}")
        return None
    
    def parse_rule_content(self, content: str) -> Tuple[Dict[str, int], List[str]]:
        """
        解析规则文件内容
        
        Args:
            content: 规则文件内容
            
        Returns:
            (统计信息, 规则列表)
        """
        lines = content.split('\n')
        rules = []
        stats = {}
        
        for line in lines:
            line = line.strip()
            
            # 跳过注释和空行
            if not line or line.startswith('#'):
                # 解析统计信息
                if line.startswith('# ') and ':' in line:
                    parts = line[2:].split(':', 1)
                    if len(parts) == 2:
                        key = parts[0].strip()
                        try:
                            value = int(parts[1].strip())
                            stats[key] = value
                        except ValueError:
                            pass
                continue
            
            # 验证规则格式
            if self._is_valid_rule(line):
                rules.append(line)
        
        return stats, rules
    
    def _is_valid_rule(self, rule: str) -> bool:
        """
        验证规则格式是否正确
        
        Args:
            rule: 规则字符串
            
        Returns:
            是否为有效规则
        """
        # Surge规则类型
        valid_types = [
            'DOMAIN', 'DOMAIN-SUFFIX', 'DOMAIN-KEYWORD',
            'IP-CIDR', 'IP-CIDR6', 'IP-ASN',
            'PROCESS-NAME', 'USER-AGENT',
            'URL-REGEX', 'AND', 'OR', 'NOT'
        ]
        
        # 检查规则是否以有效类型开头
        for rule_type in valid_types:
            if rule.startswith(rule_type + ','):
                return True
        
        return False
    
    def collect_from_source(self, source_name: str, source_config: Dict) -> Dict[str, Tuple[Dict, List[str]]]:
        """
        从指定数据源收集规则
        
        Args:
            source_name: 数据源名称
            source_config: 数据源配置
            
        Returns:
            {规则名称: (统计信息, 规则列表)}
        """
        base_url = source_config.get('base_url', '')
        rules_config = source_config.get('rules', [])
        
        results = {}
        
        # 使用线程池并发下载
        with ThreadPoolExecutor(max_workers=self.concurrent_downloads) as executor:
            # 提交所有下载任务
            future_to_rule = {}
            for rule_config in rules_config:
                rule_name = rule_config.get('name')
                rule_path = rule_config.get('path')
                
                if not rule_name or not rule_path:
                    continue
                
                url = urljoin(base_url + '/', rule_path)
                future = executor.submit(self.fetch_rule_content, url)
                future_to_rule[future] = rule_config
            
            # 处理完成的任务
            for future in as_completed(future_to_rule):
                rule_config = future_to_rule[future]
                rule_name = rule_config.get('name')
                
                try:
                    content = future.result()
                    if content:
                        stats, rules = self.parse_rule_content(content)
                        results[rule_name] = (stats, rules)
                        self.logger.info(f"成功处理规则 {rule_name}: {len(rules)} 条规则")
                    else:
                        self.logger.warning(f"规则 {rule_name} 内容为空")
                        
                except Exception as e:
                    self.logger.error(f"处理规则 {rule_name} 时出错: {e}")
        
        return results
    
    def collect_all_rules(self) -> Dict[str, Dict[str, Tuple[Dict, List[str]]]]:
        """
        收集所有配置的规则
        
        Returns:
            {数据源名称: {规则名称: (统计信息, 规则列表)}}
        """
        all_results = {}
        sources = self.config.get('sources', {})
        
        for source_name, source_config in sources.items():
            self.logger.info(f"开始收集数据源: {source_name}")
            results = self.collect_from_source(source_name, source_config)
            all_results[source_name] = results
            self.logger.info(f"完成数据源 {source_name}: 收集了 {len(results)} 个规则文件")
        
        return all_results
