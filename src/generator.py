"""
Surge规则文件生成器模块
负责生成标准格式的Surge规则文件
"""

import os
import logging
from datetime import datetime
from typing import Dict, List
from collections import defaultdict


class RuleFileGenerator:
    """规则文件生成器类"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.output_config = config.get('output', {})
        self.categories = config.get('categories', {})
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 确保输出目录存在
        self.output_dir = self.output_config.get('directory', 'rules')
        os.makedirs(self.output_dir, exist_ok=True)
    
    def generate_file_header(self, category_name: str, category_config: Dict, stats: Dict[str, int]) -> str:
        """
        生成规则文件头部信息
        
        Args:
            category_name: 分类名称
            category_config: 分类配置
            stats: 统计信息
            
        Returns:
            文件头部字符串
        """
        header_lines = []
        
        # 基本信息
        header_lines.append(f"# NAME: {category_config.get('name', category_name)}")
        header_lines.append(f"# AUTHOR: SurgeRuleCollector")
        header_lines.append(f"# REPO: https://github.com/user/surge_rules")
        header_lines.append(f"# UPDATED: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        header_lines.append(f"# DESCRIPTION: {category_config.get('description', '')}")
        
        # 统计信息
        for rule_type, count in sorted(stats.items()):
            if rule_type != 'TOTAL':
                header_lines.append(f"# {rule_type}: {count}")
        
        # 总计
        total = stats.get('TOTAL', 0)
        header_lines.append(f"# TOTAL: {total}")
        
        return '\n'.join(header_lines)
    
    def sort_rules(self, rules: List[str]) -> List[str]:
        """
        对规则进行排序
        
        Args:
            rules: 规则列表
            
        Returns:
            排序后的规则列表
        """
        # 定义规则类型的优先级
        type_priority = {
            'DOMAIN': 1,
            'DOMAIN-SUFFIX': 2,
            'DOMAIN-KEYWORD': 3,
            'IP-CIDR': 4,
            'IP-CIDR6': 5,
            'IP-ASN': 6,
            'PROCESS-NAME': 7,
            'USER-AGENT': 8,
            'URL-REGEX': 9,
            'AND': 10,
            'OR': 11,
            'NOT': 12
        }
        
        def get_sort_key(rule: str) -> tuple:
            """获取规则的排序键"""
            rule_type = rule.split(',')[0]
            priority = type_priority.get(rule_type, 999)
            
            # 在同类型内按字母顺序排序
            return (priority, rule.lower())
        
        return sorted(rules, key=get_sort_key)
    
    def generate_rule_file(self, category_name: str, rules: List[str], stats: Dict[str, int]) -> str:
        """
        生成单个规则文件内容
        
        Args:
            category_name: 分类名称
            rules: 规则列表
            stats: 统计信息
            
        Returns:
            文件内容
        """
        content_lines = []
        
        # 添加文件头部
        if self.output_config.get('include_header', True):
            category_config = self.categories.get(category_name, {})
            header = self.generate_file_header(category_name, category_config, stats)
            content_lines.append(header)
            content_lines.append('')  # 空行分隔
        
        # 添加规则
        if rules:
            sorted_rules = self.sort_rules(rules)
            content_lines.extend(sorted_rules)
        
        return '\n'.join(content_lines)
    
    def calculate_stats(self, rules: List[str]) -> Dict[str, int]:
        """
        计算规则统计信息
        
        Args:
            rules: 规则列表
            
        Returns:
            统计信息字典
        """
        stats = defaultdict(int)
        
        for rule in rules:
            rule_type = rule.split(',')[0]
            stats[rule_type] += 1
        
        stats['TOTAL'] = len(rules)
        return dict(stats)
    
    def generate_all_files(self, classified_rules: Dict[str, List[str]]) -> Dict[str, str]:
        """
        生成所有分类的规则文件
        
        Args:
            classified_rules: 分类后的规则
            
        Returns:
            {分类名称: 文件路径}
        """
        generated_files = {}
        
        for category_name, rules in classified_rules.items():
            if not rules:
                self.logger.warning(f"分类 {category_name} 没有规则，跳过生成")
                continue
            
            # 获取输出文件名
            category_config = self.categories.get(category_name, {})
            filename = category_config.get('output_file', f"{category_name}.list")
            filepath = os.path.join(self.output_dir, filename)
            
            # 计算统计信息
            stats = self.calculate_stats(rules)
            
            # 生成文件内容
            content = self.generate_rule_file(category_name, rules, stats)
            
            # 写入文件
            try:
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                generated_files[category_name] = filepath
                self.logger.info(f"生成规则文件: {filepath} ({len(rules)} 条规则)")
                
            except Exception as e:
                self.logger.error(f"生成文件 {filepath} 时出错: {e}")
        
        return generated_files
    
    def generate_summary_file(self, classified_rules: Dict[str, List[str]], generated_files: Dict[str, str]):
        """
        生成汇总文件
        
        Args:
            classified_rules: 分类后的规则
            generated_files: 生成的文件路径
        """
        summary_lines = []
        
        # 标题
        summary_lines.append("# Surge规则文件汇总")
        summary_lines.append(f"# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        summary_lines.append("")
        
        # 统计信息
        total_rules = sum(len(rules) for rules in classified_rules.values())
        summary_lines.append(f"## 总体统计")
        summary_lines.append(f"- 总规则数: {total_rules}")
        summary_lines.append(f"- 分类数: {len(classified_rules)}")
        summary_lines.append(f"- 生成文件数: {len(generated_files)}")
        summary_lines.append("")
        
        # 各分类详情
        summary_lines.append("## 分类详情")
        for category_name, rules in classified_rules.items():
            category_config = self.categories.get(category_name, {})
            category_display_name = category_config.get('name', category_name)
            description = category_config.get('description', '')
            
            summary_lines.append(f"### {category_display_name}")
            if description:
                summary_lines.append(f"- 描述: {description}")
            summary_lines.append(f"- 规则数: {len(rules)}")
            
            if category_name in generated_files:
                filename = os.path.basename(generated_files[category_name])
                summary_lines.append(f"- 文件: {filename}")
            
            # 规则类型统计
            stats = self.calculate_stats(rules)
            for rule_type, count in sorted(stats.items()):
                if rule_type != 'TOTAL':
                    summary_lines.append(f"  - {rule_type}: {count}")
            
            summary_lines.append("")
        
        # 写入汇总文件
        summary_path = os.path.join(self.output_dir, "README.md")
        try:
            with open(summary_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(summary_lines))
            
            self.logger.info(f"生成汇总文件: {summary_path}")
            
        except Exception as e:
            self.logger.error(f"生成汇总文件时出错: {e}")
