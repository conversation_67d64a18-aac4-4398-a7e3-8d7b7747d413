"""
Surge规则分类器模块
负责将收集到的规则按照类别进行分类
"""

import re
import logging
from typing import Dict, List, Tuple, Set
from collections import defaultdict


class RuleClassifier:
    """规则分类器类"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.categories = config.get('categories', {})
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 预定义的分类规则
        self._init_classification_rules()
    
    def _init_classification_rules(self):
        """初始化分类规则"""
        
        # 中国大陆相关域名关键词
        self.china_domain_keywords = {
            'baidu', 'taobao', 'tmall', 'alipay', 'aliyun', 'alibaba',
            'tencent', 'qq', 'weixin', 'wechat', 'qzone',
            'sina', 'weibo', 'sohu', 'netease', '163',
            'jd', 'meituan', 'dianping', 'douban', 'zhihu',
            'bilibili', 'iqiyi', 'youku', 'tudou',
            'ctrip', 'qunar', 'didi', 'ele',
            'xiaomi', 'huawei', 'oppo', 'vivo', 'meizu',
            'pinduoduo', 'douyin', 'tiktok'
        }
        
        # 中国大陆域名后缀
        self.china_domain_suffixes = {
            '.cn', '.com.cn', '.net.cn', '.org.cn', '.gov.cn', '.edu.cn',
            '.ac.cn', '.mil.cn', '.bj.cn', '.sh.cn', '.tj.cn', '.cq.cn'
        }
        
        # AI服务相关关键词
        self.ai_service_keywords = {
            'openai', 'chatgpt', 'claude', 'anthropic', 'gemini',
            'bard', 'copilot', 'midjourney', 'stability',
            'huggingface', 'replicate', 'runpod', 'cohere',
            'perplexity', 'character', 'janitor'
        }
        
        # Telegram相关关键词
        self.telegram_keywords = {
            'telegram', 'tg', 'telesco', 'telegra', 't.me',
            'nicegram', 'nekogram', 'nagram'
        }
        
        # 国外常见服务关键词
        self.foreign_service_keywords = {
            'google', 'youtube', 'gmail', 'gstatic', 'googleapis',
            'facebook', 'instagram', 'whatsapp', 'meta',
            'twitter', 'x.com', 'twimg', 'twitpic',
            'apple', 'icloud', 'itunes', 'appstore',
            'microsoft', 'outlook', 'office', 'xbox', 'skype',
            'amazon', 'aws', 'cloudfront', 'amazonaws',
            'netflix', 'spotify', 'discord', 'reddit',
            'github', 'gitlab', 'stackoverflow', 'medium',
            'dropbox', 'onedrive', 'googledrive'
        }
    
    def classify_rule(self, rule: str, source_category: str = None) -> str:
        """
        对单条规则进行分类
        
        Args:
            rule: 规则字符串
            source_category: 来源分类提示
            
        Returns:
            分类名称
        """
        rule_lower = rule.lower()
        
        # 如果有来源分类提示，优先使用
        if source_category and source_category in self.categories:
            return source_category
        
        # IP-ASN规则分类
        if rule.startswith('IP-ASN,'):
            # 中国大陆ASN
            china_asns = {'4134', '4837', '9808', '17623', '23724', '38365', '56040', '56041', '56042'}
            asn_match = re.search(r'IP-ASN,(\d+)', rule)
            if asn_match and asn_match.group(1) in china_asns:
                return 'china_asn'
            return 'china_asn'  # 默认归类为中国ASN，可以后续手动调整
        
        # IP-CIDR规则分类
        if rule.startswith(('IP-CIDR,', 'IP-CIDR6,')):
            # 简单的IP地址段分类，可以根据需要扩展
            return 'china_asn'  # 默认归类，可以后续优化
        
        # 域名规则分类
        if rule.startswith(('DOMAIN,', 'DOMAIN-SUFFIX,', 'DOMAIN-KEYWORD,')):
            domain_part = rule.split(',', 1)[1].split(',')[0].lower()
            
            # Telegram相关
            if any(keyword in domain_part for keyword in self.telegram_keywords):
                return 'telegram'
            
            # AI服务相关
            if any(keyword in domain_part for keyword in self.ai_service_keywords):
                return 'ai_services'
            
            # 中国大陆域名
            if (any(keyword in domain_part for keyword in self.china_domain_keywords) or
                any(domain_part.endswith(suffix) for suffix in self.china_domain_suffixes)):
                return 'china_domains'
            
            # 国外服务
            if any(keyword in domain_part for keyword in self.foreign_service_keywords):
                return 'foreign_domains'
            
            # 默认根据TLD判断
            if domain_part.endswith('.cn'):
                return 'china_domains'
            else:
                return 'foreign_domains'
        
        # 进程名规则
        if rule.startswith('PROCESS-NAME,'):
            process_name = rule.split(',', 1)[1].lower()
            if any(keyword in process_name for keyword in self.telegram_keywords):
                return 'telegram'
            return 'foreign_domains'  # 默认归类
        
        # 其他规则类型默认归类
        return 'foreign_domains'
    
    def classify_rules(self, rules_data: Dict[str, Dict[str, Tuple[Dict, List[str]]]]) -> Dict[str, List[str]]:
        """
        对所有规则进行分类
        
        Args:
            rules_data: {数据源名称: {规则名称: (统计信息, 规则列表)}}
            
        Returns:
            {分类名称: [规则列表]}
        """
        classified_rules = defaultdict(list)
        
        # 获取源配置中的分类映射
        source_category_mapping = {}
        sources = self.config.get('sources', {})
        for source_name, source_config in sources.items():
            rules_config = source_config.get('rules', [])
            for rule_config in rules_config:
                rule_name = rule_config.get('name')
                category = rule_config.get('category')
                if rule_name and category:
                    source_category_mapping[rule_name] = category
        
        # 遍历所有规则进行分类
        for source_name, source_rules in rules_data.items():
            self.logger.info(f"正在分类数据源 {source_name} 的规则")
            
            for rule_name, (stats, rules) in source_rules.items():
                source_category = source_category_mapping.get(rule_name)
                
                self.logger.info(f"正在分类规则集 {rule_name} ({len(rules)} 条规则)")
                
                for rule in rules:
                    category = self.classify_rule(rule, source_category)
                    classified_rules[category].append(rule)
        
        # 去重
        for category in classified_rules:
            classified_rules[category] = list(set(classified_rules[category]))
            self.logger.info(f"分类 {category}: {len(classified_rules[category])} 条规则")
        
        return dict(classified_rules)
    
    def get_category_stats(self, classified_rules: Dict[str, List[str]]) -> Dict[str, Dict[str, int]]:
        """
        获取分类统计信息
        
        Args:
            classified_rules: 分类后的规则
            
        Returns:
            {分类名称: {规则类型: 数量}}
        """
        stats = {}
        
        for category, rules in classified_rules.items():
            category_stats = defaultdict(int)
            
            for rule in rules:
                rule_type = rule.split(',')[0]
                category_stats[rule_type] += 1
            
            category_stats['TOTAL'] = len(rules)
            stats[category] = dict(category_stats)
        
        return stats
