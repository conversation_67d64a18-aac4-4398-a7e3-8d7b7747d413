"""
Surge规则去重模块
负责对规则进行智能去重处理
"""

import re
import logging
from typing import List, Set, Dict, Tuple
from collections import defaultdict


class RuleDeduplicator:
    """规则去重器类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def normalize_rule(self, rule: str) -> str:
        """
        规范化规则格式，用于去重比较

        Args:
            rule: 原始规则

        Returns:
            规范化后的规则
        """
        # 移除多余的空格和制表符
        rule = re.sub(r'\s+', ' ', rule.strip())

        # 规范化逗号后的空格
        rule = re.sub(r',\s+', ',', rule)

        # 统一no-resolve参数的格式
        rule = re.sub(r',\s*no-resolve\s*$', ',no-resolve', rule)

        return rule
    
    def extract_rule_key(self, rule: str) -> str:
        """
        提取规则的关键部分用于去重比较
        
        Args:
            rule: 规则字符串
            
        Returns:
            规则关键部分
        """
        parts = rule.split(',')
        if len(parts) < 2:
            return rule
        
        rule_type = parts[0].strip()
        rule_value = parts[1].strip()
        
        # 对于IP规则，忽略no-resolve参数进行去重
        if rule_type in ['IP-CIDR', 'IP-CIDR6', 'IP-ASN']:
            return f"{rule_type},{rule_value}"
        
        return rule
    
    def is_redundant_rule(self, rule1: str, rule2: str) -> bool:
        """
        检查两个规则是否存在冗余关系
        
        Args:
            rule1: 规则1
            rule2: 规则2
            
        Returns:
            是否存在冗余关系
        """
        parts1 = rule1.split(',')
        parts2 = rule2.split(',')
        
        if len(parts1) < 2 or len(parts2) < 2:
            return False
        
        type1, value1 = parts1[0].strip(), parts1[1].strip()
        type2, value2 = parts2[0].strip(), parts2[1].strip()
        
        # 检查DOMAIN和DOMAIN-SUFFIX的冗余关系
        if type1 == 'DOMAIN' and type2 == 'DOMAIN-SUFFIX':
            # 如果DOMAIN规则的域名是DOMAIN-SUFFIX规则的子域名，则DOMAIN规则冗余
            if value1.endswith('.' + value2) or value1 == value2:
                return True
        elif type1 == 'DOMAIN-SUFFIX' and type2 == 'DOMAIN':
            # 如果DOMAIN规则的域名是DOMAIN-SUFFIX规则的子域名，则DOMAIN规则冗余
            if value2.endswith('.' + value1) or value1 == value2:
                return True
        elif type1 == 'DOMAIN-SUFFIX' and type2 == 'DOMAIN-SUFFIX':
            # 如果一个后缀是另一个的子集，保留更通用的
            if value1.endswith('.' + value2):
                return True  # rule1冗余
            elif value2.endswith('.' + value1):
                return False  # rule2冗余，但这里返回False，在调用处处理
        
        return False
    
    def deduplicate_basic(self, rules: List[str]) -> List[str]:
        """
        基本去重：移除完全相同的规则
        
        Args:
            rules: 规则列表
            
        Returns:
            去重后的规则列表
        """
        if not rules:
            return []
        
        original_count = len(rules)
        
        # 使用set进行基本去重，但保持规则的规范化
        normalized_rules = {}
        for rule in rules:
            normalized = self.normalize_rule(rule)
            key = self.extract_rule_key(normalized)
            if key not in normalized_rules:
                normalized_rules[key] = normalized
        
        deduplicated = list(normalized_rules.values())
        removed_count = original_count - len(deduplicated)
        
        if removed_count > 0:
            self.logger.info(f"基本去重: 移除了 {removed_count} 条重复规则")
        
        return deduplicated
    
    def deduplicate_advanced(self, rules: List[str]) -> List[str]:
        """
        高级去重：移除冗余规则
        
        Args:
            rules: 规则列表
            
        Returns:
            去重后的规则列表
        """
        if not rules:
            return []
        
        original_count = len(rules)
        
        # 按规则类型分组
        rules_by_type = defaultdict(list)
        other_rules = []
        
        for rule in rules:
            parts = rule.split(',')
            if len(parts) >= 2:
                rule_type = parts[0].strip()
                if rule_type in ['DOMAIN', 'DOMAIN-SUFFIX', 'DOMAIN-KEYWORD']:
                    rules_by_type[rule_type].append(rule)
                else:
                    other_rules.append(rule)
            else:
                other_rules.append(rule)
        
        # 处理域名规则的冗余
        final_rules = []
        
        # 先处理DOMAIN-SUFFIX规则，找出最通用的
        domain_suffix_rules = rules_by_type.get('DOMAIN-SUFFIX', [])
        if domain_suffix_rules:
            # 按域名长度排序，短的（更通用的）在前
            domain_suffix_rules.sort(key=lambda x: len(x.split(',')[1].strip()))
            
            kept_suffixes = []
            for rule in domain_suffix_rules:
                domain = rule.split(',')[1].strip()
                is_redundant = False
                
                # 检查是否被已保留的更通用规则覆盖
                for kept_rule in kept_suffixes:
                    kept_domain = kept_rule.split(',')[1].strip()
                    if domain.endswith('.' + kept_domain) or domain == kept_domain:
                        is_redundant = True
                        break
                
                if not is_redundant:
                    kept_suffixes.append(rule)
            
            final_rules.extend(kept_suffixes)
        
        # 处理DOMAIN规则，移除被DOMAIN-SUFFIX覆盖的
        domain_rules = rules_by_type.get('DOMAIN', [])
        if domain_rules:
            kept_domains = []
            suffix_domains = [rule.split(',')[1].strip() for rule in final_rules if rule.startswith('DOMAIN-SUFFIX,')]
            
            for rule in domain_rules:
                domain = rule.split(',')[1].strip()
                is_covered = False
                
                # 检查是否被DOMAIN-SUFFIX规则覆盖
                for suffix_domain in suffix_domains:
                    if domain.endswith('.' + suffix_domain) or domain == suffix_domain:
                        is_covered = True
                        break
                
                if not is_covered:
                    kept_domains.append(rule)
            
            final_rules.extend(kept_domains)
        
        # 添加DOMAIN-KEYWORD和其他规则
        final_rules.extend(rules_by_type.get('DOMAIN-KEYWORD', []))
        final_rules.extend(other_rules)
        
        removed_count = original_count - len(final_rules)
        if removed_count > 0:
            self.logger.info(f"高级去重: 移除了 {removed_count} 条冗余规则")
        
        return final_rules
    
    def deduplicate_rules(self, rules: List[str], enable_advanced: bool = True) -> Tuple[List[str], Dict[str, int]]:
        """
        对规则列表进行完整去重
        
        Args:
            rules: 规则列表
            enable_advanced: 是否启用高级去重
            
        Returns:
            (去重后的规则列表, 去重统计信息)
        """
        if not rules:
            return [], {'original': 0, 'deduplicated': 0, 'removed': 0}
        
        original_count = len(rules)
        self.logger.info(f"开始去重处理: {original_count} 条规则")
        
        # 基本去重
        deduplicated_rules = self.deduplicate_basic(rules)
        basic_removed = original_count - len(deduplicated_rules)
        
        # 高级去重
        advanced_removed = 0
        if enable_advanced:
            before_advanced = len(deduplicated_rules)
            deduplicated_rules = self.deduplicate_advanced(deduplicated_rules)
            advanced_removed = before_advanced - len(deduplicated_rules)
        
        final_count = len(deduplicated_rules)
        total_removed = original_count - final_count
        
        stats = {
            'original': original_count,
            'deduplicated': final_count,
            'removed': total_removed,
            'basic_removed': basic_removed,
            'advanced_removed': advanced_removed
        }
        
        if total_removed > 0:
            self.logger.info(f"去重完成: 原始 {original_count} 条 -> 最终 {final_count} 条 (移除 {total_removed} 条)")
        
        return deduplicated_rules, stats
    
    def deduplicate_by_category(self, classified_rules: Dict[str, List[str]], 
                               enable_advanced: bool = True) -> Tuple[Dict[str, List[str]], Dict[str, Dict[str, int]]]:
        """
        按分类对规则进行去重
        
        Args:
            classified_rules: 分类后的规则
            enable_advanced: 是否启用高级去重
            
        Returns:
            (去重后的分类规则, 各分类的去重统计)
        """
        deduplicated_rules = {}
        all_stats = {}
        
        for category, rules in classified_rules.items():
            self.logger.info(f"正在对分类 '{category}' 进行去重")
            deduplicated, stats = self.deduplicate_rules(rules, enable_advanced)
            deduplicated_rules[category] = deduplicated
            all_stats[category] = stats
        
        return deduplicated_rules, all_stats
