# Surge规则收集器

一个用于收集和整理Surge分流规则的Python工具，支持从GitHub等源自动收集规则并按类别分类生成标准格式的规则文件。

## 功能特性

- 🚀 **自动收集**: 从多个数据源自动收集Surge规则
- 📂 **智能分类**: 自动将规则分类为不同类别
- 📝 **标准格式**: 生成符合Surge标准的规则文件
- ⚡ **并发下载**: 支持多线程并发下载提高效率
- 🔧 **可配置**: 通过YAML配置文件灵活配置

## 支持的规则分类

- **China IP-ASN**: 中国大陆IP地址段和ASN
- **China Domains**: 中国大陆常见域名
- **Foreign Domains**: 国外常见域名
- **Telegram**: Telegram相关域名和IP
- **AI Services**: AI服务相关域名和IP

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 配置文件

编辑 `config.yaml` 文件来配置数据源和分类规则。

### 2. 命令行使用

```bash
# 查看帮助
python main.py --help

# 运行完整流程 (推荐)
python main.py run

# 分步执行
python main.py collect    # 收集规则
python main.py classify   # 分类规则
python main.py generate   # 生成文件

# 清理临时文件
python main.py clean
```

### 3. 输出文件

生成的规则文件将保存在 `rules/` 目录下：

- `china_asn.list` - 中国大陆IP-ASN规则
- `china_domains.list` - 中国大陆域名规则
- `foreign_domains.list` - 国外域名规则
- `telegram.list` - Telegram规则
- `ai_services.list` - AI服务规则
- `README.md` - 汇总信息

## 配置说明

### 数据源配置

在 `config.yaml` 中的 `sources` 部分配置数据源：

```yaml
sources:
  blackmatrix7:
    base_url: "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Surge"
    rules:
      - name: "Telegram"
        path: "Telegram/Telegram.list"
        category: "telegram"
```

### 分类配置

在 `categories` 部分配置输出分类：

```yaml
categories:
  telegram:
    name: "Telegram"
    description: "Telegram相关域名和IP"
    output_file: "telegram.list"
```

## 项目结构

```
surge_rules/
├── main.py              # 主程序入口
├── config.yaml          # 配置文件
├── requirements.txt     # Python依赖
├── src/                 # 源代码目录
│   ├── __init__.py
│   ├── collector.py     # 规则收集器
│   ├── classifier.py    # 规则分类器
│   └── generator.py     # 文件生成器
├── rules/               # 输出目录
└── tests/               # 测试目录
```

## 开发说明

### 添加新的数据源

1. 在 `config.yaml` 的 `sources` 部分添加新的数据源配置
2. 如需特殊处理，可在 `collector.py` 中扩展

### 添加新的分类

1. 在 `config.yaml` 的 `categories` 部分添加新分类
2. 在 `classifier.py` 中添加相应的分类逻辑

### 自定义规则格式

如需支持其他代理工具的规则格式，可以扩展 `generator.py` 模块。

## 注意事项

- 请确保网络连接正常，能够访问GitHub等数据源
- 首次运行可能需要较长时间下载所有规则文件
- 建议定期运行以获取最新的规则更新

## 许可证

MIT License
