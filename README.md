# Surge 规则收集器

一个用于收集和整理 Surge 分流规则的 Python 工具，支持从 GitHub 等源自动收集规则并按类别分类生成标准格式的规则文件。

## 功能特性

- 🚀 **自动收集**: 从多个数据源自动收集 Surge 规则
- 📂 **智能分类**: 自动将规则分类为不同类别
- 🔄 **智能去重**: 多层次去重，移除重复和冗余规则
- 🌐 **代理支持**: 支持通过代理访问被墙的数据源
- 📝 **标准格式**: 生成符合 Surge 标准的规则文件
- ⚡ **并发下载**: 支持多线程并发下载提高效率
- 🔧 **可配置**: 通过 YAML 配置文件灵活配置

## 支持的规则分类

- **China IP-ASN**: 中国大陆 IP 地址段和 ASN
- **China Domains**: 中国大陆常见域名
- **Foreign Domains**: 国外常见域名
- **Telegram**: Telegram 相关域名和 IP
- **AI Services**: AI 服务相关域名和 IP

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 配置文件

编辑 `config.yaml` 文件来配置数据源和分类规则。

### 2. 命令行使用

```bash
# 查看帮助
python main.py --help

# 运行完整流程 (推荐)
python main.py run

# 分步执行
python main.py collect    # 收集规则
python main.py classify   # 分类规则
python main.py generate   # 生成文件

# 清理临时文件
python main.py clean
```

### 3. 输出文件

生成的规则文件将保存在 `rules/` 目录下：

- `china_asn.list` - 中国大陆 IP-ASN 规则
- `china_domains.list` - 中国大陆域名规则
- `foreign_domains.list` - 国外域名规则
- `telegram.list` - Telegram 规则
- `ai_services.list` - AI 服务规则
- `README.md` - 汇总信息

## 配置说明

### 数据源配置

新版本采用按分类组织多数据源的结构，在 `config.yaml` 中配置：

```yaml
categories:
  telegram:
    name: "Telegram"
    description: "Telegram相关域名和IP"
    output_file: "telegram.list"
    sources:
      - name: "blackmatrix7-telegram"
        url: "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Surge/Telegram/Telegram.list"
      - name: "v2fly-telegram"
        url: "https://cdn.jsdelivr.net/gh/v2fly/domain-list-community@release/telegram.txt"
```

### 代理配置

对于需要通过代理访问的数据源，可以配置代理：

```yaml
proxy:
  # 代理服务器地址
  http_proxy: "http://127.0.0.1:6152"
  https_proxy: "http://127.0.0.1:6152"

  # 需要使用代理的域名列表
  proxy_domains:
    - "github.com"
    - "githubusercontent.com"
    - "cdn.jsdelivr.net"

  # 不使用代理的域名列表（优先级更高）
  no_proxy_domains:
    - "localhost"
    - "127.0.0.1"
```

## 项目结构

```
surge_rules/
├── main.py              # 主程序入口
├── config.yaml          # 配置文件
├── requirements.txt     # Python依赖
├── src/                 # 源代码目录
│   ├── __init__.py
│   ├── collector.py     # 规则收集器
│   ├── classifier.py    # 规则分类器
│   └── generator.py     # 文件生成器
├── rules/               # 输出目录
└── tests/               # 测试目录
```

## 开发说明

### 添加新的数据源

1. 在 `config.yaml` 的 `sources` 部分添加新的数据源配置
2. 如需特殊处理，可在 `collector.py` 中扩展

### 添加新的分类

1. 在 `config.yaml` 的 `categories` 部分添加新分类
2. 在 `classifier.py` 中添加相应的分类逻辑

### 自定义规则格式

如需支持其他代理工具的规则格式，可以扩展 `generator.py` 模块。

## 去重功能说明

程序提供了多层次的智能去重功能：

### 基本去重

- 移除完全相同的规则
- 规范化规则格式（统一空格、no-resolve 参数等）
- 处理格式不同但内容相同的规则

### 高级去重

- **域名规则优化**:
  - `DOMAIN,api.example.com` + `DOMAIN-SUFFIX,example.com` → 保留 `DOMAIN-SUFFIX,example.com`
  - `DOMAIN-SUFFIX,sub.example.com` + `DOMAIN-SUFFIX,example.com` → 保留 `DOMAIN-SUFFIX,example.com`
- **智能覆盖**: 自动识别被更通用规则覆盖的具体规则
- **规则排序**: 按类型和字母顺序排序，便于维护

### 去重配置

在 `config.yaml` 中可以控制去重行为：

```yaml
deduplication:
  enable_basic: true # 启用基本去重
  enable_advanced: true # 启用高级去重
  enable_collection_dedup: true # 收集阶段去重
  enable_classification_dedup: true # 分类阶段去重
  enable_generation_dedup: true # 生成阶段去重
```

## 注意事项

- 请确保网络连接正常，能够访问 GitHub 等数据源
- 首次运行可能需要较长时间下载所有规则文件
- 建议定期运行以获取最新的规则更新
- 去重功能会自动优化规则，减少文件大小和提高匹配效率

## 许可证

MIT License
