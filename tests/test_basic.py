#!/usr/bin/env python3
"""
基本功能测试
"""

import os
import sys
import unittest
import tempfile
import shutil
from unittest.mock import patch, MagicMock

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from collector import RuleCollector
from classifier import RuleClassifier
from generator import RuleFileGenerator


class TestRuleCollector(unittest.TestCase):
    """测试规则收集器"""
    
    def setUp(self):
        self.config = {
            'settings': {
                'user_agent': 'TestAgent/1.0',
                'timeout': 10,
                'retry_count': 2,
                'concurrent_downloads': 2
            },
            'sources': {
                'test_source': {
                    'base_url': 'https://example.com',
                    'rules': [
                        {
                            'name': 'TestRule',
                            'path': 'test.list',
                            'category': 'test'
                        }
                    ]
                }
            }
        }
        self.collector = RuleCollector(self.config)
    
    def test_parse_rule_content(self):
        """测试规则内容解析"""
        content = """# NAME: Test
# DOMAIN: 2
# TOTAL: 3
DOMAIN,example.com
DOMAIN-SUFFIX,test.com
IP-CIDR,*******/32,no-resolve
"""
        stats, rules = self.collector.parse_rule_content(content)
        
        self.assertEqual(stats['DOMAIN'], 2)
        self.assertEqual(stats['TOTAL'], 3)
        self.assertEqual(len(rules), 3)
        self.assertIn('DOMAIN,example.com', rules)
    
    def test_is_valid_rule(self):
        """测试规则验证"""
        valid_rules = [
            'DOMAIN,example.com',
            'DOMAIN-SUFFIX,test.com',
            'IP-CIDR,*******/32,no-resolve',
            'IP-ASN,12345,no-resolve'
        ]
        
        invalid_rules = [
            'INVALID,example.com',
            'example.com',
            '',
            '# comment'
        ]
        
        for rule in valid_rules:
            self.assertTrue(self.collector._is_valid_rule(rule), f"应该是有效规则: {rule}")
        
        for rule in invalid_rules:
            self.assertFalse(self.collector._is_valid_rule(rule), f"应该是无效规则: {rule}")


class TestRuleClassifier(unittest.TestCase):
    """测试规则分类器"""
    
    def setUp(self):
        self.config = {
            'categories': {
                'china_domains': {'name': 'China Domains'},
                'foreign_domains': {'name': 'Foreign Domains'},
                'telegram': {'name': 'Telegram'},
                'ai_services': {'name': 'AI Services'},
                'china_asn': {'name': 'China ASN'}
            },
            'sources': {}
        }
        self.classifier = RuleClassifier(self.config)
    
    def test_classify_rule(self):
        """测试单条规则分类"""
        test_cases = [
            ('DOMAIN,t.me', 'telegram'),
            ('DOMAIN-SUFFIX,baidu.com', 'china_domains'),
            ('DOMAIN,google.com', 'foreign_domains'),
            ('DOMAIN,openai.com', 'ai_services'),
            ('IP-ASN,4134,no-resolve', 'china_asn'),
            ('IP-CIDR,*******/32,no-resolve', 'china_asn')
        ]
        
        for rule, expected_category in test_cases:
            result = self.classifier.classify_rule(rule)
            self.assertEqual(result, expected_category, f"规则 {rule} 应该分类为 {expected_category}，实际为 {result}")


class TestRuleFileGenerator(unittest.TestCase):
    """测试规则文件生成器"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.config = {
            'output': {
                'directory': self.temp_dir,
                'include_header': True,
                'include_stats': True
            },
            'categories': {
                'test_category': {
                    'name': 'Test Category',
                    'description': 'Test description',
                    'output_file': 'test.list'
                }
            }
        }
        self.generator = RuleFileGenerator(self.config)
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir)
    
    def test_calculate_stats(self):
        """测试统计计算"""
        rules = [
            'DOMAIN,example.com',
            'DOMAIN,test.com',
            'DOMAIN-SUFFIX,example.org',
            'IP-CIDR,*******/32,no-resolve'
        ]
        
        stats = self.generator.calculate_stats(rules)
        
        self.assertEqual(stats['DOMAIN'], 2)
        self.assertEqual(stats['DOMAIN-SUFFIX'], 1)
        self.assertEqual(stats['IP-CIDR'], 1)
        self.assertEqual(stats['TOTAL'], 4)
    
    def test_sort_rules(self):
        """测试规则排序"""
        rules = [
            'IP-CIDR,*******/32,no-resolve',
            'DOMAIN,example.com',
            'DOMAIN-SUFFIX,test.com',
            'DOMAIN,apple.com'
        ]
        
        sorted_rules = self.generator.sort_rules(rules)
        
        # DOMAIN类型应该排在前面
        self.assertTrue(sorted_rules[0].startswith('DOMAIN,'))
        self.assertTrue(sorted_rules[1].startswith('DOMAIN,'))
        # DOMAIN-SUFFIX应该在DOMAIN之后
        self.assertTrue(sorted_rules[2].startswith('DOMAIN-SUFFIX,'))
        # IP-CIDR应该在最后
        self.assertTrue(sorted_rules[3].startswith('IP-CIDR,'))
    
    def test_generate_rule_file(self):
        """测试规则文件生成"""
        rules = [
            'DOMAIN,example.com',
            'DOMAIN-SUFFIX,test.com'
        ]
        stats = {'DOMAIN': 1, 'DOMAIN-SUFFIX': 1, 'TOTAL': 2}
        
        content = self.generator.generate_rule_file('test_category', rules, stats)
        
        # 检查是否包含头部信息
        self.assertIn('# NAME: Test Category', content)
        self.assertIn('# DESCRIPTION: Test description', content)
        self.assertIn('# DOMAIN: 1', content)
        self.assertIn('# TOTAL: 2', content)
        
        # 检查是否包含规则
        self.assertIn('DOMAIN,example.com', content)
        self.assertIn('DOMAIN-SUFFIX,test.com', content)


if __name__ == '__main__':
    unittest.main()
