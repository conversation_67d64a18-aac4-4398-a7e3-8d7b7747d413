#!/usr/bin/env python3
"""
Surge规则收集器主程序
"""

import os
import sys
import yaml
import click
import logging
from typing import Dict

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from collector import RuleCollector
from classifier import RuleClassifier
from generator import RuleFileGenerator


def load_config(config_path: str) -> Dict:
    """
    加载配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        配置字典
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        click.echo(f"错误: 无法加载配置文件 {config_path}: {e}", err=True)
        sys.exit(1)


def setup_logging(verbose: bool = False):
    """
    设置日志
    
    Args:
        verbose: 是否显示详细日志
    """
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


@click.group()
@click.option('--config', '-c', default='config.yaml', help='配置文件路径')
@click.option('--verbose', '-v', is_flag=True, help='显示详细日志')
@click.pass_context
def cli(ctx, config, verbose):
    """Surge规则收集器 - 收集和整理Surge分流规则"""
    setup_logging(verbose)
    
    # 加载配置
    if not os.path.exists(config):
        click.echo(f"错误: 配置文件 {config} 不存在", err=True)
        sys.exit(1)
    
    ctx.ensure_object(dict)
    ctx.obj['config'] = load_config(config)
    ctx.obj['verbose'] = verbose


@cli.command()
@click.pass_context
def collect(ctx):
    """收集规则文件"""
    config = ctx.obj['config']
    
    click.echo("开始收集规则...")
    
    # 创建收集器
    collector = RuleCollector(config)
    
    # 收集所有规则
    rules_data = collector.collect_all_rules()
    
    # 显示收集结果
    total_sources = len(rules_data)
    total_rule_files = sum(len(source_rules) for source_rules in rules_data.values())
    total_rules = sum(
        len(rules) for source_rules in rules_data.values()
        for stats, rules in source_rules.values()
    )
    
    click.echo(f"收集完成:")
    click.echo(f"  - 数据源: {total_sources}")
    click.echo(f"  - 规则文件: {total_rule_files}")
    click.echo(f"  - 总规则数: {total_rules}")
    
    # 保存收集结果到临时文件（可选）
    import json
    with open('collected_rules.json', 'w', encoding='utf-8') as f:
        # 转换为可序列化的格式
        serializable_data = {}
        for source_name, source_rules in rules_data.items():
            serializable_data[source_name] = {}
            for rule_name, (stats, rules) in source_rules.items():
                serializable_data[source_name][rule_name] = {
                    'stats': stats,
                    'rules': rules
                }
        json.dump(serializable_data, f, ensure_ascii=False, indent=2)
    
    click.echo("收集结果已保存到 collected_rules.json")


@cli.command()
@click.pass_context
def classify(ctx):
    """对规则进行分类"""
    config = ctx.obj['config']
    
    # 检查是否存在收集结果
    if not os.path.exists('collected_rules.json'):
        click.echo("错误: 请先运行 'collect' 命令收集规则", err=True)
        sys.exit(1)
    
    click.echo("开始分类规则...")
    
    # 加载收集结果
    import json
    with open('collected_rules.json', 'r', encoding='utf-8') as f:
        serialized_data = json.load(f)
    
    # 转换回原始格式
    rules_data = {}
    for source_name, source_rules in serialized_data.items():
        rules_data[source_name] = {}
        for rule_name, rule_data in source_rules.items():
            stats = rule_data['stats']
            rules = rule_data['rules']
            rules_data[source_name][rule_name] = (stats, rules)
    
    # 创建分类器
    classifier = RuleClassifier(config)
    
    # 分类规则
    classified_rules = classifier.classify_rules(rules_data)
    
    # 显示分类结果
    click.echo("分类完成:")
    for category, rules in classified_rules.items():
        category_config = config.get('categories', {}).get(category, {})
        category_name = category_config.get('name', category)
        click.echo(f"  - {category_name}: {len(rules)} 条规则")
    
    # 保存分类结果
    with open('classified_rules.json', 'w', encoding='utf-8') as f:
        json.dump(classified_rules, f, ensure_ascii=False, indent=2)
    
    click.echo("分类结果已保存到 classified_rules.json")


@cli.command()
@click.pass_context
def generate(ctx):
    """生成规则文件"""
    config = ctx.obj['config']
    
    # 检查是否存在分类结果
    if not os.path.exists('classified_rules.json'):
        click.echo("错误: 请先运行 'classify' 命令分类规则", err=True)
        sys.exit(1)
    
    click.echo("开始生成规则文件...")
    
    # 加载分类结果
    import json
    with open('classified_rules.json', 'r', encoding='utf-8') as f:
        classified_rules = json.load(f)
    
    # 创建生成器
    generator = RuleFileGenerator(config)
    
    # 生成所有文件
    generated_files = generator.generate_all_files(classified_rules)
    
    # 生成汇总文件
    generator.generate_summary_file(classified_rules, generated_files)
    
    # 显示生成结果
    click.echo("生成完成:")
    for category, filepath in generated_files.items():
        category_config = config.get('categories', {}).get(category, {})
        category_name = category_config.get('name', category)
        filename = os.path.basename(filepath)
        rule_count = len(classified_rules.get(category, []))
        click.echo(f"  - {category_name}: {filename} ({rule_count} 条规则)")


@cli.command()
@click.pass_context
def run(ctx):
    """运行完整流程 (收集 -> 分类 -> 生成)"""
    click.echo("开始运行完整流程...")
    
    # 依次执行各个步骤
    ctx.invoke(collect)
    click.echo()
    ctx.invoke(classify)
    click.echo()
    ctx.invoke(generate)
    
    click.echo("\n✅ 完整流程执行完成!")


@cli.command()
@click.pass_context
def clean(ctx):
    """清理临时文件"""
    temp_files = ['collected_rules.json', 'classified_rules.json']
    
    for temp_file in temp_files:
        if os.path.exists(temp_file):
            os.remove(temp_file)
            click.echo(f"已删除: {temp_file}")
        else:
            click.echo(f"文件不存在: {temp_file}")


if __name__ == '__main__':
    cli()
